import type { CitizenInfo } from '@/interfaces/staff';
import apiClient from '@/api/axios-instance';

const apiScan = import.meta.env.VITE_SCAN_CITIZENT_URL;

export const extractIdentifi = async (
  frontFile: File,
  backFile: File,
): Promise<CitizenInfo> => {
  const formData = new FormData();
  formData.append('front', frontFile);
  formData.append('back', backFile);

  const response = await fetch(`${apiScan}/citizen/scan`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data: CitizenInfo = await response.json();

  return data;
};

export const applyExtractedData = async (
  userId: number,
  frontFile: File,
  backFile: File,
  extractedInfo: CitizenInfo['info'],
): Promise<boolean> => {
  try {
    const formData = new FormData();

    // Add image files với tên field phù hợp với PHP backend
    formData.append('id_card_front', frontFile);
    formData.append('id_card_back', backFile);

    // Add extracted data
    formData.append('staff_id', userId.toString());
    formData.append(
      'identification_number',
      extractedInfo.personal_identification,
    );
    formData.append('fullname_vn', extractedInfo.full_name);
    formData.append('sex', extractedInfo.sex.toLowerCase());
    formData.append('current_address', extractedInfo.add_str);
    formData.append('birthday', extractedInfo.dob);
    formData.append('issue_date', extractedInfo.date_issue);
    formData.append(
      'place_of_issue',
      extractedInfo.director_general_police_department,
    );

    const { data } = await apiClient.post('/profile/upload-id-card', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // Kiểm tra response từ PHP backend
    if (data.success === true) {
      return true;
    } else {
      throw new Error(data.error || data.message || 'Upload failed');
    }
  } catch (error) {
    console.error('Error applying extracted data:', error);
    throw error;
  }
};
