import type {
  CitizenInfo,
  ScanIDCardResponse,
  ScanIDCardUpload,
} from '@/interfaces/staff';

import apiClient from '@/api/axios-instance';

const apiScan = import.meta.env.VITE_SCAN_CITIZENT_URL;

export const extractIdentifi = async (
  frontFile: File,
  backFile: File,
): Promise<CitizenInfo> => {
  const formData = new FormData();
  formData.append('front', frontFile);
  formData.append('back', backFile);

  const response = await fetch(`${apiScan}/citizen/scan`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data: CitizenInfo = await response.json();

  return data;
};

export const applyExtractedData = async (
  frontFile: File,
  backFile: File,
  payload: ScanIDCardUpload,
): Promise<ScanIDCardResponse> => {
  try {
    // Debug: <PERSON><PERSON><PERSON> tra files trước khi gửi
    console.log('Frontend Debug:', {
      frontFile: frontFile
        ? { name: frontFile.name, size: frontFile.size, type: frontFile.type }
        : null,
      backFile: backFile
        ? { name: backFile.name, size: backFile.size, type: backFile.type }
        : null,
    });

    if (!frontFile || !backFile) {
      throw new Error('Missing front or back file');
    }

    const formData = new FormData();

    // Add image files với tên field phù hợp với PHP backend
    formData.append('id_card_front', frontFile, frontFile.name);
    formData.append('id_card_back', backFile, backFile.name);

    // Add extracted data
    formData.append('identification', payload.identification || '');
    formData.append('fullname', payload.fullname || '');
    formData.append('sex', payload.gender?.toLowerCase() || '');
    formData.append('current_address', payload.current_address || '');
    formData.append('birthday', payload.date_of_birth || '');
    formData.append('issue_date', payload.issue_date || '');
    formData.append('place_of_issue', payload.place_of_issue || '');

    // Debug: Log FormData contents
    console.log('FormData contents:');
    for (const [key, value] of formData.entries()) {
      console.log(key, value);
    }

    const { data } = await apiClient.post('/profile/upload-id-card', formData, {
      headers: {
        // Không set Content-Type, để browser tự động set với boundary
        // 'Content-Type': 'multipart/form-data',
      },
    });

    return data;
  } catch (error) {
    console.error('Error applying extracted data:', error);
    throw error;
  }
};
