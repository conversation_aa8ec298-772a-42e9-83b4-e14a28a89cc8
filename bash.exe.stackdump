Stack trace:
Frame         Function      Args
0007FFFFBE40  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFAD40) msys-2.0.dll+0x2118E
0007FFFFBE40  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFC118) msys-2.0.dll+0x69BA
0007FFFFBE40  0002100469F2 (00021028DF99, 0007FFFFBCF8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBE40  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBE40  00021006A545 (0007FFFFBE50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFC120  00021006B9A5 (0007FFFFBE50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9AF8C0000 ntdll.dll
7FF9AF260000 KERNEL32.DLL
7FF9ACB10000 KERNELBASE.dll
7FF9A9370000 apphelp.dll
7FF9AE830000 USER32.dll
7FF9AD460000 win32u.dll
7FF9ADB10000 GDI32.dll
7FF9AD330000 gdi32full.dll
7FF9AD490000 msvcp_win.dll
7FF9AD540000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9AEA20000 advapi32.dll
7FF9AD7A0000 msvcrt.dll
7FF9AD850000 sechost.dll
7FF9AE330000 RPCRT4.dll
7FF9AC070000 CRYPTBASE.DLL
7FF9AD290000 bcryptPrimitives.dll
7FF9AD690000 IMM32.DLL
