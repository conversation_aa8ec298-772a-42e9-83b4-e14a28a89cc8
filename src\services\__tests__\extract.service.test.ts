import { describe, it, expect, vi, beforeEach } from 'vitest';
import { extractIdentifi, applyExtractedData } from '../extract.service';
import apiClient from '@/api/axios-instance';
import type { CitizenInfo } from '@/interfaces/staff';

// Mock the API client
vi.mock('@/api/axios-instance');
const mockedApiClient = vi.mocked(apiClient);

// Mock environment variable
vi.mock('import.meta.env', () => ({
  VITE_SCAN_CITIZENT_URL: 'http://localhost:3000'
}));

// Mock fetch
global.fetch = vi.fn();

describe('Extract Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('extractIdentifi', () => {
    it('should extract citizen information from ID card images', async () => {
      const mockResponse: CitizenInfo = {
        status_code: 200,
        message: 'Success',
        info: {
          citizen_id: 123456789,
          full_name: '<PERSON><PERSON><PERSON>',
          dob: '01/01/1990',
          sex: 'Male',
          add_str: '123 Main St, Ho Chi Minh City',
          personal_identification: '123456789012',
          date_issue: '01/01/2020',
          director_general_police_department: 'Ho Chi Minh City Police'
        }
      };

      const mockFetch = vi.mocked(fetch);
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      } as Response);

      const frontFile = new File(['front'], 'front.jpg', { type: 'image/jpeg' });
      const backFile = new File(['back'], 'back.jpg', { type: 'image/jpeg' });

      const result = await extractIdentifi(frontFile, backFile);

      expect(result).toEqual(mockResponse);
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/citizen/scan',
        expect.objectContaining({
          method: 'POST',
          body: expect.any(FormData)
        })
      );
    });

    it('should throw error when API returns error', async () => {
      const mockFetch = vi.mocked(fetch);
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400
      } as Response);

      const frontFile = new File(['front'], 'front.jpg', { type: 'image/jpeg' });
      const backFile = new File(['back'], 'back.jpg', { type: 'image/jpeg' });

      await expect(extractIdentifi(frontFile, backFile)).rejects.toThrow('HTTP error! status: 400');
    });
  });

  describe('applyExtractedData', () => {
    it('should apply extracted data with images to backend', async () => {
      const mockResponse = { success: true };
      mockedApiClient.post.mockResolvedValueOnce({ data: mockResponse });

      const userId = 1;
      const frontFile = new File(['front'], 'front.jpg', { type: 'image/jpeg' });
      const backFile = new File(['back'], 'back.jpg', { type: 'image/jpeg' });
      const extractedInfo: CitizenInfo['info'] = {
        citizen_id: 123456789,
        full_name: 'Nguyen Van A',
        dob: '01/01/1990',
        sex: 'Male',
        add_str: '123 Main St, Ho Chi Minh City',
        personal_identification: '123456789012',
        date_issue: '01/01/2020',
        director_general_police_department: 'Ho Chi Minh City Police'
      };

      const result = await applyExtractedData(userId, frontFile, backFile, extractedInfo);

      expect(result).toBe(true);
      expect(mockedApiClient.post).toHaveBeenCalledWith(
        '/staff/apply-id-card-data',
        expect.any(FormData),
        expect.objectContaining({
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
      );
    });

    it('should throw error when API call fails', async () => {
      mockedApiClient.post.mockRejectedValueOnce(new Error('API Error'));

      const userId = 1;
      const frontFile = new File(['front'], 'front.jpg', { type: 'image/jpeg' });
      const backFile = new File(['back'], 'back.jpg', { type: 'image/jpeg' });
      const extractedInfo: CitizenInfo['info'] = {
        citizen_id: 123456789,
        full_name: 'Nguyen Van A',
        dob: '01/01/1990',
        sex: 'Male',
        add_str: '123 Main St, Ho Chi Minh City',
        personal_identification: '123456789012',
        date_issue: '01/01/2020',
        director_general_police_department: 'Ho Chi Minh City Police'
      };

      await expect(applyExtractedData(userId, frontFile, backFile, extractedInfo)).rejects.toThrow('API Error');
    });
  });
});
