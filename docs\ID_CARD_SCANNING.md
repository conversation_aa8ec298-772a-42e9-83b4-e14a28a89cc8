# ID Card Scanning Service

## Tổng quan

Service này cho phép người dùng scan CMND/CCCD và tự động áp dụng thông tin vào profile của họ.

## Luồng hoạt động

### 1. Scan ID Card (`ScanIdCardView.vue`)

1. **Upload ảnh**: Người dùng upload ảnh mặt trước và mặt sau của CMND/CCCD
2. **Extract thông tin**: Gọi API để trích xuất thông tin từ ảnh
3. **Hiển thị kết quả**: Hiển thị thông tin đã trích xuất
4. **Apply dữ liệu**: G<PERSON><PERSON> ảnh và thông tin lên backend để lưu trữ

### 2. Apply Data Process

Khi người dùng nhấn "Apply":
1. Gửi FormData bao gồm:
   - `front`: File ảnh mặt trước
   - `back`: File ảnh mặt sau  
   - `staff_id`: ID của user
   - `identification`: Số CMND/CCCD
   - `fullname_vn`: <PERSON>ọ tên
   - `gender`: <PERSON><PERSON><PERSON><PERSON> tính
   - `current_address`: Đ<PERSON><PERSON> chỉ hiện tại
   - `date_of_birth`: Ngày sinh
   - `issue_date`: Ngày cấp
   - `place_of_issue`: Nơi cấp

2. Backend lưu ảnh và cập nhật thông tin user
3. Frontend reload thông tin user
4. Chuyển hướng đến trang edit profile

## API Endpoints

### Extract Information
```
POST {VITE_SCAN_CITIZENT_URL}/citizen/scan
Content-Type: multipart/form-data

Body:
- front: File (ảnh mặt trước)
- back: File (ảnh mặt sau)

Response:
{
  "status_code": 200,
  "message": "Success",
  "info": {
    "citizen_id": number,
    "full_name": string,
    "dob": string,
    "sex": string,
    "add_str": string,
    "personal_identification": string,
    "date_issue": string,
    "director_general_police_department": string
  }
}
```

### Apply Extracted Data
```
POST /staff/apply-id-card-data
Content-Type: multipart/form-data

Body:
- front: File (ảnh mặt trước)
- back: File (ảnh mặt sau)
- staff_id: string
- identification: string
- fullname_vn: string
- gender: string
- current_address: string
- date_of_birth: string
- issue_date: string
- place_of_issue: string

Response:
{
  "success": boolean
}
```

## Files liên quan

### Services
- `src/services/extract.service.ts`: Service chính cho scan và apply data

### Views
- `src/views/ScanIdCardView.vue`: Giao diện scan ID card
- `src/views/EditProfileView.vue`: Giao diện edit profile (nhận dữ liệu sau khi apply)

### Composables
- `src/composables/useEditProfile.ts`: Logic xử lý edit profile

### Interfaces
- `src/interfaces/staff.ts`: Type definitions cho CitizenInfo

### Translations
- `src/locales/vi/profile.json`: Tiếng Việt
- `src/locales/en/profile.json`: Tiếng Anh  
- `src/locales/ja/profile.json`: Tiếng Nhật

## Cách sử dụng

### 1. Import service
```typescript
import { extractIdentifi, applyExtractedData } from '@/services/extract.service';
```

### 2. Extract thông tin
```typescript
const extractedData = await extractIdentifi(frontFile, backFile);
```

### 3. Apply dữ liệu
```typescript
const success = await applyExtractedData(
  userId, 
  frontFile, 
  backFile, 
  extractedData.info
);
```

## Error Handling

Service sẽ throw error trong các trường hợp:
- API scan trả về lỗi
- API apply trả về lỗi
- Network error
- File upload error

Các error này được handle trong component và hiển thị toast message cho user.

## Testing

Chạy test:
```bash
npm run test src/services/__tests__/extract.service.test.ts
```

## Environment Variables

Cần thiết lập:
```
VITE_SCAN_CITIZENT_URL=http://your-scan-api-url
```
