<?php
/**
 * Endpoint: POST /profile/upload-id-card
 * <PERSON><PERSON> lý upload ảnh CMND/CCCD và cập nhật thông tin user
 */

class ProfileController 
{
    private $upload_dir = 'uploads/id_cards';
    private $db; // Database connection

    public function __construct($database_connection) 
    {
        $this->db = $database_connection;
        
        // Tạo thư mục upload nếu chưa tồn tại
        if (!file_exists($this->upload_dir)) {
            mkdir($this->upload_dir, 0755, true);
        }
    }

    public function upload_id_card() 
    {
        try {
            // Kiểm tra method
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                return $this->json_response(false, 'Method not allowed');
            }

            // Kiểm tra authentication (JWT token, session, etc.)
            $user_id = $this->get_authenticated_user_id();
            if (!$user_id) {
                return $this->json_response(false, 'Unauthorized');
            }

            // Validate required fields
            $required_fields = ['staff_id', 'identification_number', 'fullname_vn'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    return $this->json_response(false, "Missing required field: $field");
                }
            }

            // Validate files
            if (!isset($_FILES['id_card_front']) || !isset($_FILES['id_card_back'])) {
                return $this->json_response(false, 'Both front and back ID card images are required');
            }

            // Upload front image
            $front_result = $this->handle_id_card_upload($_FILES['id_card_front'], $this->upload_dir, 'front');
            if (!$front_result['success']) {
                return $this->json_response(false, 'Front image upload failed: ' . $front_result['error']);
            }

            // Upload back image
            $back_result = $this->handle_id_card_upload($_FILES['id_card_back'], $this->upload_dir, 'back');
            if (!$back_result['success']) {
                // Xóa front image nếu back upload thất bại
                unlink($front_result['file_path']);
                return $this->json_response(false, 'Back image upload failed: ' . $back_result['error']);
            }

            // Chuẩn bị dữ liệu để update database
            $update_data = [
                'identification_number' => $_POST['identification_number'],
                'fullname_vn' => $_POST['fullname_vn'],
                'sex' => $_POST['sex'] ?? null,
                'current_address' => $_POST['current_address'] ?? null,
                'birthday' => $this->format_date($_POST['birthday'] ?? null),
                'issue_date' => $this->format_date($_POST['issue_date'] ?? null),
                'place_of_issue' => $_POST['place_of_issue'] ?? null,
                'id_card_front_image' => $front_result['filename'],
                'id_card_back_image' => $back_result['filename'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Update database
            $update_success = $this->update_user_profile($_POST['staff_id'], $update_data);
            
            if ($update_success) {
                return $this->json_response(true, 'ID card uploaded and profile updated successfully', [
                    'front_image' => $front_result['filename'],
                    'back_image' => $back_result['filename']
                ]);
            } else {
                // Xóa uploaded files nếu database update thất bại
                unlink($front_result['file_path']);
                unlink($back_result['file_path']);
                return $this->json_response(false, 'Failed to update profile in database');
            }

        } catch (Exception $e) {
            error_log('ID Card Upload Error: ' . $e->getMessage());
            return $this->json_response(false, 'Internal server error');
        }
    }

    private function handle_id_card_upload($file, $upload_dir, $type)
    {
        // Kiểm tra lỗi upload
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return [
                'success' => false,
                'error' => 'Upload error: ' . $this->get_upload_error_message($file['error'])
            ];
        }

        // Kiểm tra loại file
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowed_types)) {
            return [
                'success' => false,
                'error' => 'Invalid file type. Only JPEG, PNG, and GIF are allowed.'
            ];
        }

        // Kiểm tra kích thước file (max 5MB)
        $max_size = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $max_size) {
            return [
                'success' => false,
                'error' => 'File size too large. Maximum 5MB allowed.'
            ];
        }

        // Tạo tên file unique
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'id_card_' . $type . '_' . time() . '_' . uniqid() . '.' . $file_extension;
        $file_path = $upload_dir . '/' . $filename;

        // Upload file
        if (move_uploaded_file($file['tmp_name'], $file_path)) {
            // Tạo thumbnail nếu cần
            $this->create_thumbnail($file_path, $upload_dir, $filename);
            
            return [
                'success' => true,
                'filename' => $filename,
                'file_path' => $file_path
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Failed to move uploaded file'
            ];
        }
    }

    private function create_thumbnail($source_path, $upload_dir, $filename)
    {
        // Tạo thumbnail 300x200 cho preview
        $thumbnail_dir = $upload_dir . '/thumbnails';
        if (!file_exists($thumbnail_dir)) {
            mkdir($thumbnail_dir, 0755, true);
        }

        $thumbnail_path = $thumbnail_dir . '/thumb_' . $filename;
        
        // Sử dụng GD library để resize image
        $this->resize_image($source_path, $thumbnail_path, 300, 200);
    }

    private function resize_image($source, $destination, $width, $height)
    {
        $info = getimagesize($source);
        $mime = $info['mime'];

        switch ($mime) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($source);
                break;
            case 'image/png':
                $image = imagecreatefrompng($source);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($source);
                break;
            default:
                return false;
        }

        $thumb = imagecreatetruecolor($width, $height);
        imagecopyresampled($thumb, $image, 0, 0, 0, 0, $width, $height, imagesx($image), imagesy($image));

        switch ($mime) {
            case 'image/jpeg':
                imagejpeg($thumb, $destination, 90);
                break;
            case 'image/png':
                imagepng($thumb, $destination);
                break;
            case 'image/gif':
                imagegif($thumb, $destination);
                break;
        }

        imagedestroy($image);
        imagedestroy($thumb);
        return true;
    }

    private function format_date($date_string)
    {
        if (empty($date_string)) return null;
        
        // Convert từ DD/MM/YYYY sang YYYY-MM-DD
        if (preg_match('/^(\d{2})\/(\d{2})\/(\d{4})$/', $date_string, $matches)) {
            return $matches[3] . '-' . $matches[2] . '-' . $matches[1];
        }
        
        return $date_string;
    }

    private function update_user_profile($staff_id, $data)
    {
        try {
            $set_clauses = [];
            $params = [];
            
            foreach ($data as $key => $value) {
                if ($value !== null) {
                    $set_clauses[] = "$key = ?";
                    $params[] = $value;
                }
            }
            
            $params[] = $staff_id;
            
            $sql = "UPDATE users SET " . implode(', ', $set_clauses) . " WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            
            return $stmt->execute($params);
        } catch (Exception $e) {
            error_log('Database update error: ' . $e->getMessage());
            return false;
        }
    }

    private function get_authenticated_user_id()
    {
        // Implement authentication logic
        // Ví dụ: check JWT token, session, etc.
        // Return user ID nếu authenticated, false nếu không
        
        // Placeholder implementation
        return $_POST['staff_id'] ?? false;
    }

    private function get_upload_error_message($error_code)
    {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }

    private function json_response($success, $message, $data = null)
    {
        header('Content-Type: application/json');
        $response = [
            'success' => $success,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        echo json_encode($response);
        exit;
    }
}

// Usage
// $controller = new ProfileController($database_connection);
// $controller->upload_id_card();
?>
