<!DOCTYPE html>
<html>
<head>
    <title>Debug Upload Test</title>
</head>
<body>
    <h1>Test ID Card Upload</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div>
            <label>Front Image:</label>
            <input type="file" name="id_card_front" accept="image/*" required>
        </div>
        <div>
            <label>Back Image:</label>
            <input type="file" name="id_card_back" accept="image/*" required>
        </div>
        <div>
            <label>Staff ID:</label>
            <input type="text" name="staff_id" value="1" required>
        </div>
        <div>
            <label>Identification Number:</label>
            <input type="text" name="identification_number" value="123456789012" required>
        </div>
        <div>
            <label>Full Name:</label>
            <input type="text" name="fullname_vn" value="Nguyen Van A" required>
        </div>
        <div>
            <label>Sex:</label>
            <select name="sex">
                <option value="male">Male</option>
                <option value="female">Female</option>
            </select>
        </div>
        <div>
            <label>Current Address:</label>
            <input type="text" name="current_address" value="123 Main St">
        </div>
        <div>
            <label>Birthday:</label>
            <input type="text" name="birthday" value="01/01/1990">
        </div>
        <div>
            <label>Issue Date:</label>
            <input type="text" name="issue_date" value="01/01/2020">
        </div>
        <div>
            <label>Place of Issue:</label>
            <input type="text" name="place_of_issue" value="Ho Chi Minh City Police">
        </div>
        <br>
        <button type="submit">Upload</button>
    </form>

    <div id="result"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            // Debug: Log FormData contents
            console.log('FormData contents:');
            for (let [key, value] of formData.entries()) {
                console.log(key, value);
            }
            
            try {
                resultDiv.innerHTML = 'Uploading...';
                
                const response = await fetch('/profile/upload-id-card', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                resultDiv.innerHTML = `
                    <h3>Response:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                    <p>Status: ${response.status}</p>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        });
    </script>
</body>
</html>
