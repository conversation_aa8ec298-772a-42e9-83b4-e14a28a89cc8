export interface Staff {
  staff_id: number;
  staff_fullname_vn: string;
  staff_fullname_en: string | null;
  staff_email: string;
  staff_identifi: string;
  role_id: number;
  role_name: string;
  is_admin: boolean;
  first_login: number;
}

export interface StaffInfo {
  staff_id: number;
  full_name: string;
  staff_identifi: string;
  email: string;
}

export interface EmployeeInfo {
  id: number;
  type: string;
  employee_id: number;
  staff_id: number;
  staff_fullname_vn: string;
  staff_identifi: string;
  staff_email: string;
}

export interface ManagementInfo {
  manager_id: number;
  subordinate_count: number;
  can_approve_requests: boolean;
}

import type { ApiResponse } from '@/types';
import type { UserGender } from '@/enums/UserGender';

export interface User {
  id: number;
  company_email: string;
  personal_email: string;
  profile_image: string;
  fullname_vn: string;
  fullname_en: string;
  start_work_date: string;
  birthday: string;
  birth_place: string;
  sex: UserGender;
  staff_identifi: string;
  home_town: string;
  current_address: string;
  phone_number: string;
  position_name: string;
  department_name: string;
  workplace_name: string;
  workplace_address: string;
  latitude: string;
  longitude: string;
  language: string;
  identification_number: string;
  place_of_issue: string;
  issue_date: string;
  days_for_identity: string | null;
  religion: string;
  nation: string;
  account_number: string;
  name_account: string;
  issue_bank: string;
  personal_tax_code: string;
  social_security_no: string;
  literacy:
    | 'none'
    | 'primary'
    | 'secondary'
    | 'high_school'
    | 'college_vocational'
    | 'bachelor'
    | 'master'
    | 'doctorate'
    | string;
  hourly_rate: string;
  role: Role;
  first_login: boolean;
  vehicle_plate: string;
  marital_status: string;
  major: string;
}
export interface Role {
  id: number;
  name: string;
}

export interface CitizenInfo {
  info: {
    citizen_id: number;
    full_name: string;
    dob: string;
    sex: string;
    add_str: string;
    personal_identification: string;
    date_issue: string;
    director_general_police_department: string;
  };
  message: string;
  status_code: number;
}

export type StaffResponse = ApiResponse<Staff[]>;
