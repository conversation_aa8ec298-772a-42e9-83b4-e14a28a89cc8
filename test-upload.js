// Test script đ<PERSON> kiểm tra upload ID card
// Chạy trong browser console

async function testUpload() {
  // Tạo mock files
  const frontFile = new File(['front image data'], 'front.jpg', { type: 'image/jpeg' });
  const backFile = new File(['back image data'], 'back.jpg', { type: 'image/jpeg' });
  
  // Tạo payload
  const payload = {
    staff_id: 1,
    identification: '123456789012',
    fullname: '<PERSON><PERSON><PERSON>',
    gender: 'male',
    current_address: '123 Main St, Ho Chi Minh City',
    date_of_birth: '01/01/1990',
    issue_date: '01/01/2020',
    place_of_issue: 'Ho Chi Minh City Police'
  };
  
  console.log('Testing upload with:', {
    frontFile: { name: frontFile.name, size: frontFile.size, type: frontFile.type },
    backFile: { name: backFile.name, size: backFile.size, type: backFile.type },
    payload
  });
  
  // Tạo FormData
  const formData = new FormData();
  formData.append('id_card_front', frontFile, frontFile.name);
  formData.append('id_card_back', backFile, backFile.name);
  
  Object.keys(payload).forEach(key => {
    formData.append(key === 'fullname' ? 'fullname_vn' : key, payload[key]);
  });
  
  // Log FormData contents
  console.log('FormData contents:');
  for (const [key, value] of formData.entries()) {
    console.log(key, value);
  }
  
  try {
    const response = await fetch('/profile/upload-id-card', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    console.log('Response:', {
      status: response.status,
      statusText: response.statusText,
      data: result
    });
    
    return result;
  } catch (error) {
    console.error('Upload error:', error);
    throw error;
  }
}

// Chạy test
testUpload();
