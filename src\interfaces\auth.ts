export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  status: boolean;
  message: string;
  data: {
    staff_id: string;
    staff_email: string;
    staff_role: string;
    staff_role_name: string;
    staff_logged_in: boolean;
    expiration_time: number;
    first_login: number;
    token: string;
  };
}
export interface ForgotPasswordResponse {
  message: string;
}

export interface LoginResult {
  success: boolean;
  message: string;
  first_login?: number;
}
