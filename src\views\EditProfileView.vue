<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useDateFormats } from '@/composables/useDateFormats';
import { useEditProfile } from '@/composables/useEditProfile';
import { useHeaderActionsStore } from '@/stores/header-actions';
import { useUserStore } from '@/stores/user';
import { IonPage } from '@ionic/vue';
import { Briefcase, CreditCard, Phone, User } from 'lucide-vue-next';
import {
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
} from 'naive-ui';
import { h, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const userStore = useUserStore();
const headerActionsStore = useHeaderActionsStore();
const { dateFormats } = useDateFormats();

// Use composable for profile editing
const {
  formData,
  isSubmitting,
  // isLoading,
  genderOptions,
  educationOptions,
  maritalStatusOptions,
  initializeForm,
  handleSubmit,
  toVietnamDate,
} = useEditProfile();

// Function to convert Vietnam date to timestamp for form data
const vietnamDateToTimestamp = (date: Date): number => {
  // Ensure the date is treated as local time (Vietnam timezone)
  return new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime();
};

onMounted(async () => {
  await initializeForm();
});

onMounted(() => {
  headerActionsStore.set(() =>
    h(
      'button',
      {
        class:
          'p-2 transition-colors hover:bg-gray-100 cursor-pointer rounded-md text-gray-800 font-semibold text-base',
        onClick: handleSubmit,

        disabled: isSubmitting.value,
      },
      isSubmitting.value ? t('profile.edit.saving') : t('profile.edit.save'),
    ),
  );
});

onUnmounted(() => {
  headerActionsStore.clear();
});
</script>

<template>
  <IonPage>
    <div class="scroll-container flex h-full flex-col items-center gap-y-4 bg-gray-50">
      <NForm label-placement="top" class="w-full space-y-6 p-4">
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="flex flex-col items-center">
            <div class="relative">
              <div class="flex h-24 w-24 items-center justify-center overflow-hidden rounded-full bg-gray-200">
                <img v-if="userStore.user?.profile_image" :src="userStore.user?.profile_image" alt="Profile"
                  class="h-full w-full object-cover" />
                <User v-else class="h-12 w-12 text-gray-400" />
              </div>
            </div>
          </div>
        </div>

        <!-- Personal Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <User class="mr-2 h-5 w-5 " />
          {{ t('profile.edit.personal_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">

          <NFormItem :label="t('profile.full_name')">
            <NInput v-model:value="formData.fullname_vn" :placeholder="t('profile.full_name')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.birthday')">
            <NDatePicker v-model:value="formData.birthday" class="w-full" :format="dateFormats.date.display"
              :placeholder="dateFormats.date.display" :is-date-disabled="() => false" :time-zone="'Asia/Ho_Chi_Minh'"
              @update:value="(value) => {
                if (value) {
                  const date = toVietnamDate(value);
                  formData.birthday = vietnamDateToTimestamp(date);
                }
              }" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.birth_place')">
            <NInput v-model:value="formData.birth_place" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.gender')">
            <NSelect v-model:value="formData.sex" :options="genderOptions"
              :placeholder="t('profile.edit.select_gender')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.home_town')">
            <NInput v-model:value="formData.home_town" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.current_address')">
            <NInput v-model:value="formData.current_address" type="textarea" placeholder="" class="h-[80px]" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.religion')">
            <NInput v-model:value="formData.religion" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.nation')">
            <NInput v-model:value="formData.nation" :placeholder="t('profile.edit.select_nation')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.education_level')">
            <NSelect v-model:value="formData.literacy" :options="educationOptions"
              :placeholder="t('profile.edit.select_education_level')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.major')">
            <NInput v-model:value="formData.major" :placeholder="t('profile.edit.major_placeholder')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.marital_status')">
            <NSelect v-model:value="formData.marital_status" :options="maritalStatusOptions"
              :placeholder="t('profile.edit.select_marital_status')" />
          </NFormItem>
        </div>

        <!-- Contact Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <Phone class="mr-2 h-5 w-5" />
          {{ t('profile.edit.contact_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <NFormItem :label="t('profile.company_email')" class="mb-3">
            <NInput v-model:value="formData.company_email" disabled
              :placeholder="t('profile.edit.company_email_placeholder')" />
            <template #feedback>
              <p class="mt-1 text-xs text-gray-500">
                {{ t('profile.edit.email_feedback') }}
              </p>
            </template>
          </NFormItem>

          <NFormItem :label="t('profile.edit.phone_number')">
            <NInput v-model:value="formData.phone_number" :placeholder="t('profile.edit.phone_number_placeholder')" />
          </NFormItem>
        </div>

        <!-- Work Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <Briefcase class="mr-2 h-5 w-5 " />
          {{ t('profile.edit.work_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <NFormItem :label="t('profile.edit.staff_id')" class="mb-3">
            <NInput v-model:value="formData.staff_identifi" placeholder="" disabled />
            <template #feedback>
              <p class="mt-1 text-xs text-gray-500">
                {{ t('profile.edit.staff_id_feedback') }}
              </p>
            </template>
          </NFormItem>

          <NFormItem :label="t('profile.edit.position')">
            <NInput v-model:value="formData.position_name" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.department')">
            <NInput v-model:value="formData.department_name" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.workplace')">
            <NInput v-model:value="formData.workplace_name" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.workplace_address')">
            <NInput v-model:value="formData.workplace_address" type="textarea" placeholder="" class="h-[80px]" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.start_work_date')">
            <NDatePicker v-model:value="formData.start_work_date" :placeholder="dateFormats.date.display" disabled
              :format="dateFormats.date.display" class="w-full" :is-date-disabled="() => false"
              :time-zone="'Asia/Ho_Chi_Minh'" @update:value="(value) => {
                if (value) {
                  const date = toVietnamDate(value);
                  formData.start_work_date = vietnamDateToTimestamp(date);
                }
              }" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.vehicle_plate')">
            <NInput v-model:value="formData.vehicle_plate" :placeholder="t('profile.edit.vehicle_plate_placeholder')" />
          </NFormItem>
        </div>

        <!-- Identity Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <CreditCard class="mr-2 h-5 w-5 " />
          {{ t('profile.edit.identity_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <NFormItem :label="t('profile.edit.identification_number')" class="mb-3">
            <NInput v-model:value="formData.identification_number" placeholder="" disabled />
            <template #feedback>
              <p class="mt-1 text-xs text-gray-500">
                {{ t('profile.edit.identification_number_feedback') }}
              </p>
            </template>
          </NFormItem>

          <NFormItem :label="t('profile.edit.place_of_issue')">
            <NInput v-model:value="formData.place_of_issue" placeholder="" disabled />
          </NFormItem>

          <NFormItem :label="t('profile.edit.issue_date')">
            <NDatePicker v-model:value="formData.issue_date" :placeholder="dateFormats.date.display" disabled
              :format="dateFormats.date.display" class="w-full" :is-date-disabled="() => false"
              :time-zone="'Asia/Ho_Chi_Minh'" @update:value="(value) => {
                if (value) {
                  const date = toVietnamDate(value);
                  formData.issue_date = vietnamDateToTimestamp(date);
                }
              }" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.personal_tax_code')">
            <NInput v-model:value="formData.personal_tax_code" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.social_insurance_number')">
            <NInput v-model:value="formData.social_security_no" placeholder="" />
          </NFormItem>
        </div>

        <!-- Banking Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <CreditCard class="mr-2 h-5 w-5 " />
          {{ t('profile.edit.banking_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <NFormItem :label="t('profile.edit.account_number')">
            <NInput v-model:value="formData.account_number" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.account_name')">
            <NInput v-model:value="formData.name_account" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.bank_name')">
            <NInput v-model:value="formData.issue_bank" placeholder="" />
          </NFormItem>
        </div>

        <Button size="lg" type="submit" @click="handleSubmit" class="mb-4 w-full">
          {{ t('profile.edit.save_changes') }}
        </Button>
      </NForm>
    </div>
  </IonPage>
</template>
